// table.tsx
import * as React from 'react'
import { cn } from '@/app/lib/utils'

export const Table = React.forwardRef<
    HTMLTableElement,
    React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
    <div className={cn('relative w-full overflow-auto', className)}>
        <table
            ref={ref}
            cellSpacing={20}
            className="w-full caption-bottom"
            {...props}
        />
    </div>
))
Table.displayName = 'Table'

export const TableHeader = React.forwardRef<
    HTMLTableSectionElement,
    React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
    <thead
        ref={ref}
        className={cn('[&_tr]:border-border', className)}
        {...props}
    />
))
TableHeader.displayName = 'TableHeader'

export const TableBody = React.forwardRef<
    HTMLTableSectionElement,
    React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, children, ...props }, ref) => (
    <tbody
        ref={ref}
        className={cn('[&_tr:last-child]:border-0', className)}
        {...props}>
        {children}
    </tbody>
))
TableBody.displayName = 'TableBody'

export const TableFooter = React.forwardRef<
    HTMLTableSectionElement,
    React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
    <tfoot
        ref={ref}
        className={cn(
            'border-t border-border bg-muted/50 font-medium [&>tr]:last:border-b-0',
            className,
        )}
        {...props}
    />
))
TableFooter.displayName = 'TableFooter'

export const TableRow = React.forwardRef<
    HTMLTableRowElement,
    React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
    <tr
        ref={ref}
        className={cn(
            'border-b cursor-pointer border-curious-blue-100 group relative data-[state=selected]:bg-curious-blue-50',
            className,
        )}
        {...props}>
        {props.children}
    </tr>
))
TableRow.displayName = 'TableRow'

export const TableHead = React.forwardRef<
    HTMLTableCellElement,
    React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
    <th
        ref={ref}
        className={cn(
            'h-8 px-[9px] pb-2 small:p-auto cursor-default relative z-10 bg-background text-sm text-muted-foreground font-normal [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',
            className,
        )}
        {...props}
    />
))
TableHead.displayName = 'TableHead'

export const TableCell = React.forwardRef<
    HTMLTableCellElement,
    React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
    <td
        ref={ref}
        className={cn(
            'h-20 relative font-normal align-center text-outer-space-800 [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] phablet:first:pl-2.5 phablet:last:pr-2.5',
            className,
        )}
        {...props}>
        <span
            className={cn(
                'absolute inset-0',
                'bg-curious-blue-50',
                'w-0', // start collapsed
                'group-hover:w-full', // expand on row hover
                'transition-[width] ease-out duration-300',
                'pointer-events-none', // allow clicks through
                'will-change-transform will-change-width',
            )}
        />
        <div className="relative z-10">{props.children}</div>
    </td>
))
TableCell.displayName = 'TableCell'

export const TableCaption = React.forwardRef<
    HTMLTableCaptionElement,
    React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
    <caption
        ref={ref}
        className={cn('mt-4 text-muted-foreground', className)}
        {...props}
    />
))
TableCaption.displayName = 'TableCaption'
